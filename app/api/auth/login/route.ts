import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/database';
import { verifyPassword, generateToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Vérifie la présence des champs requis
    if (!body.email || !body.password || !body.userType) {
      return NextResponse.json({ error: 'Champs manquants' }, { status: 400 });
    }

    const { email, password, userType } = body;

    let user: any;
    if (userType === 'admin') {
      user = db.prepare('SELECT * FROM admin_users WHERE email = ?').get(email);
      // Add role field for admin users since admin_users table doesn't have it
      if (user) {
        user.role = 'admin';
      }
    } else {
      user = db.prepare('SELECT * FROM users WHERE email = ?').get(email);
    }

    if (!user) {
      return NextResponse.json({ error: 'Email ou mot de passe incorrect' }, { status: 401 });
    }

    if (!verifyPassword(password, user.password)) {
      return NextResponse.json({ error: 'Email ou mot de passe incorrect' }, { status: 401 });
    }

    const token = generateToken(user);

    const response = NextResponse.json({
      message: 'Connexion réussie',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: userType === 'admin' ? 'admin' : user.role
      }
    });

    response.cookies.set('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 // 7 jours
    });

    return response;
  } catch (error) {
    console.error('Erreur dans /api/auth/login:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}